Locales["fr"] = {
    ["sex"] = "sexe",
    ["mom"] = "visage de la mère",
    ["dad"] = "visage du père",
    ["resemblance"] = "ressemblance",
    ["skin_tone"] = "teint",
    ["nose_1"] = "largeur du nez",
    ["nose_2"] = "hauteur du pic du nez",
    ["nose_3"] = "longueur du pic du nez",
    ["nose_4"] = "hauteur de l'os du nez",
    ["nose_5"] = "abaissement du pic du nez",
    ["nose_6"] = "torsion de l'os du nez",
    ["cheeks_1"] = "hauteur des pommettes",
    ["cheeks_2"] = "largeur des pommettes",
    ["cheeks_3"] = "largeur des joues",
    ["lip_fullness"] = "plénitude des lèvres",
    ["jaw_bone_width"] = "largeur de l'os de la mâchoire",
    ["jaw_bone_length"] = "longueur de l'os de la mâchoire",
    ["chin_height"] = "hauteur du menton",
    ["chin_length"] = "longueur du menton",
    ["chin_width"] = "largeur du menton",
    ["chin_hole"] = "taille du trou du menton",
    ["neck_thickness"] = "épaisseur du cou",
    ["wrinkles"] = "les rides",
    ["wrinkle_thickness"] = "épaisseur des rides",
    ["beard_type"] = "type de barbe",
    ["beard_size"] = "taille de barbe",
    ["beard_color_1"] = "couleur de barbe 1",
    ["beard_color_2"] = "couleur de barbe 2",
    ["hair_1"] = "cheveux 1",
    ["hair_2"] = "cheveux 2",
    ["hair_color_1"] = "couleur de cheveux 1",
    ["hair_color_2"] = "couleur de cheveux 2",
    ["eye_color"] = "couleur des yeux",
    ["eye_squint"] = "Louchement des yeux",
    ["eyebrow_type"] = "type de sourcil",
    ["eyebrow_size"] = "taille des sourcils",
    ["eyebrow_color_1"] = "couleur des sourcils 1",
    ["eyebrow_color_2"] = "couleur des sourcils 2",
    ["eyebrow_depth"] = "profondeur des sourcils",
    ["eyebrow_height"] = "hauteur des sourcils",
    ["makeup_type"] = "type de maquillage",
    ["makeup_thickness"] = "épaisseur de maquillage",
    ["makeup_color_1"] = "couleur de maquillage 1",
    ["makeup_color_2"] = "couleur de maquillage 2",
    ["lipstick_type"] = "type de rouge à lèvres",
    ["lipstick_thickness"] = "épaisseur du rouge à lèvres",
    ["lipstick_color_1"] = "couleur de rouge à lèvres 1",
    ["lipstick_color_2"] = "couleur de rouge à lèvres 2",
    ["ear_accessories"] = "accessoires d'oreille",
    ["ear_accessories_color"] = "couleur des accessoires d'oreille",
    ["tshirt_1"] = "t-Shirt 1",
    ["tshirt_2"] = "t-Shirt 2",
    ["torso_1"] = "torse 1",
    ["torso_2"] = "torse 2",
    ["decals_1"] = "décalcomanies 1",
    ["decals_2"] = "décalcomanies 2",
    ["arms"] = "bras",
    ["arms_2"] = "bras 2",
    ["pants_1"] = "pantalon 1",
    ["pants_2"] = "pantalon 2",
    ["shoes_1"] = "chaussures 1",
    ["shoes_2"] = "chaussures 2",
    ["mask_1"] = "masque 1",
    ["mask_2"] = "masque 2",
    ["bproof_1"] = "gilet pare-balles 1",
    ["bproof_2"] = "gilet pare-balles 2",
    ["chain_1"] = "chaîne 1",
    ["chain_2"] = "chaîne 2",
    ["helmet_1"] = "casque 1",
    ["helmet_2"] = "casque 2",
    ["watches_1"] = "montres 1",
    ["watches_2"] = "montres 2",
    ["bracelets_1"] = "bracelets 1",
    ["bracelets_2"] = "bracelets 2",
    ["glasses_1"] = "lunettes 1",
    ["glasses_2"] = "lunettes 2",
    ["bag"] = "sac",
    ["bag_color"] = "couleur du sac",
    ["blemishes"] = "imperfections",
    ["blemishes_size"] = "épaisseur des imperfections",
    ["ageing"] = "vieillissement",
    ["ageing_1"] = "épaisseur de vieillissement",
    ["blush"] = "rougir",
    ["blush_1"] = "épaisseur du fard à joues",
    ["blush_color"] = "couleur fard à joues",
    ["complexion"] = "complexion",
    ["complexion_1"] = "épaisseur du teint",
    ["sun"] = "soleil",
    ["sun_1"] = "épaisseur du soleil",
    ["freckles"] = "taches de rousseur",
    ["freckles_1"] = "épaisseur des taches de rousseur",
    ["chest_hair"] = "les poils du torse",
    ["chest_hair_1"] = "épaisseur des poils de la poitrine",
    ["chest_color"] = "couleur des cheveux de la poitrine",
    ["bodyb"] = "imperfections du corps",
    ["bodyb_size"] = "épaisseur des imperfections corporelles",
    ["bodyb_extra"] = "effet corps",
    ["bodyb_extra_thickness"] = "imperfections effet corps épaisseur",
}
