body {
  margin: 0;
  padding: 0;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  background: transparent;
  display: none; /* Hidden by default */
  height: 100vh;
  overflow: hidden;
}

.garage-container {
  display: flex;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
}

.sidebar {
  width: 300px;
  background: linear-gradient(135deg, #4a5568, #2d3748);
  color: white;
  display: flex;
  flex-direction: column;
}

.garage-header {
  padding: 30px 20px;
  background: linear-gradient(135deg, #5a67d8, #4c51bf);
  font-size: 28px;
  font-weight: bold;
  text-align: left;
}

.sidebar-tabs {
  flex: 1;
  padding: 0;
}

.tab {
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: background 0.3s;
  font-size: 16px;
}

.tab:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tab.active {
  background: rgba(255, 255, 255, 0.3);
  border-left: 4px solid #5a67d8;
}

.close-btn {
  padding: 20px;
  text-align: center;
  background: rgba(220, 38, 38, 0.8);
  color: white;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: background 0.3s;
}

.close-btn:hover {
  background: rgba(220, 38, 38, 1);
}

.main-content {
  flex: 1;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.vehicle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 40px;
  max-width: 800px;
  width: 100%;
}

.vehicle-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  color: #2d3748;
}

.vehicle-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.vehicle-card.locked {
  background: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  opacity: 0.6;
}

.vehicle-card.locked:hover {
  transform: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.vehicle-name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #2d3748;
}

.vehicle-price {
  font-size: 16px;
  color: #38a169;
  margin-bottom: 10px;
  font-weight: 600;
}

.vehicle-status {
  font-size: 14px;
  padding: 5px 10px;
  border-radius: 15px;
  display: inline-block;
  margin-top: 5px;
}

.status-available {
  background: #c6f6d5;
  color: #22543d;
}

.status-locked {
  background: #fed7d7;
  color: #742a2a;
}

.no-vehicles {
  color: white;
  font-size: 24px;
  text-align: center;
  opacity: 0.8;
}
