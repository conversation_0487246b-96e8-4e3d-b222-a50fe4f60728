body {
  margin: 0;
  padding: 0;
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
  background: transparent;
  display: none; /* Hidden by default */
  height: 100vh;
  overflow: hidden;
}

.garage-container {
  display: flex;
  height: 70vh;
  width: 85vw;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(15px);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.7),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

.sidebar {
  width: 220px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.garage-header {
  padding: 30px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  font-size: 22px;
  font-weight: bold;
  text-align: center;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
  letter-spacing: 3px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  position: relative;
}

.garage-header::before {
  content: "🔧";
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
}

.garage-header::after {
  content: "🔧";
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
}

.sidebar-tabs {
  flex: 1;
  padding: 20px 0;
}

.tab {
  padding: 15px 20px;
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.tab:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab.active {
  background: rgba(255, 255, 255, 0.15);
  border-left: 3px solid white;
}

.close-btn {
  padding: 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.main-content {
  flex: 1;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.vehicle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 25px;
  padding: 35px;
  max-width: 1000px;
  width: 100%;
}

.vehicle-card {
  animation: cardSlideIn 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.vehicle-card:nth-child(1) { animation-delay: 0.1s; }
.vehicle-card:nth-child(2) { animation-delay: 0.2s; }
.vehicle-card:nth-child(3) { animation-delay: 0.3s; }
.vehicle-card:nth-child(4) { animation-delay: 0.4s; }
.vehicle-card:nth-child(5) { animation-delay: 0.5s; }
.vehicle-card:nth-child(6) { animation-delay: 0.6s; }
.vehicle-card:nth-child(7) { animation-delay: 0.7s; }

@keyframes cardSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.vehicle-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 25px;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
  color: white;
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.vehicle-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.vehicle-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.12);
}

.vehicle-card:hover::before {
  left: 100%;
}

.vehicle-card.locked {
  background: rgba(255, 255, 255, 0.05);
  cursor: not-allowed;
  opacity: 0.5;
  border-color: rgba(255, 255, 255, 0.1);
}

.vehicle-card.locked:hover {
  transform: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.vehicle-image {
  width: 100%;
  height: 80px;
  margin-bottom: 15px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Vehicle-specific styling with CSS art */
.vehicle-image::after {
  content: "";
  position: absolute;
  width: 60px;
  height: 25px;
  background: linear-gradient(45deg, #fff, #ccc);
  border-radius: 15px 5px 5px 15px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.vehicle-image[data-vehicle="adder"]::after {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  border-radius: 20px 3px 3px 20px;
  height: 20px;
}

.vehicle-image[data-vehicle="zentorno"]::after {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  border-radius: 25px 2px 2px 25px;
  height: 18px;
}

.vehicle-image[data-vehicle="t20"]::after {
  background: linear-gradient(45deg, #45b7d1, #96c93d);
  border-radius: 30px 1px 1px 30px;
  height: 16px;
}

.vehicle-image[data-vehicle="insurgent"]::after {
  background: linear-gradient(45deg, #6c5ce7, #a29bfe);
  border-radius: 8px;
  height: 35px;
  width: 55px;
}

.vehicle-image[data-vehicle="kuruma2"]::after {
  background: linear-gradient(45deg, #fd79a8, #fdcb6e);
  border-radius: 12px 8px 8px 12px;
  height: 28px;
}

.vehicle-image[data-vehicle="elegy2"]::after {
  background: linear-gradient(45deg, #00b894, #00cec9);
  border-radius: 18px 6px 6px 18px;
  height: 22px;
}

.vehicle-image[data-vehicle="buzzard2"]::after {
  background: linear-gradient(45deg, #2d3436, #636e72);
  border-radius: 50% 50% 10px 10px;
  height: 30px;
  width: 50px;
}

.vehicle-image[data-vehicle="buzzard2"]::before {
  content: "";
  position: absolute;
  width: 70px;
  height: 2px;
  background: #ddd;
  top: 25px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.vehicle-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: white;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.vehicle-price {
  font-size: 16px;
  color: #00ff88;
  margin-bottom: 10px;
  font-weight: 600;
}

.vehicle-status {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  display: inline-block;
  margin-top: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: bold;
}

.status-available {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid #00ff88;
}

.status-locked {
  background: rgba(255, 69, 69, 0.2);
  color: #ff4545;
  border: 1px solid #ff4545;
}

.purchase-btn {
  margin-top: 15px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #00ff88, #00cc6a);
  color: #000;
  border: none;
  border-radius: 25px;
  font-weight: bold;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
}

.purchase-btn:hover {
  background: linear-gradient(135deg, #00cc6a, #00aa55);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
}

.locked-btn {
  margin-top: 15px;
  padding: 12px 20px;
  background: rgba(255, 69, 69, 0.2);
  color: #ff4545;
  border: 1px solid #ff4545;
  border-radius: 25px;
  font-weight: bold;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: not-allowed;
  opacity: 0.7;
}

.no-vehicles {
  color: white;
  font-size: 24px;
  text-align: center;
  opacity: 0.8;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
