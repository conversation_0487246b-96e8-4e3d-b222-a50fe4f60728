body {
  margin: 0;
  padding: 0;
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
  background: transparent;
  display: none; /* Hidden by default */
  height: 100vh;
  overflow: hidden;
}

.garage-container {
  display: flex;
  height: 60vh;
  width: 70vw;
  background: rgba(0, 0, 0, 0.8);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 10px;
  overflow: hidden;
}

.sidebar {
  width: 200px;
  background: linear-gradient(135deg, #000080, #191970);
  color: white;
  display: flex;
  flex-direction: column;
}

.garage-header {
  padding: 20px 15px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  font-size: 18px;
  font-weight: bold;
  text-align: left;
  color: #000080;
}

.sidebar-tabs {
  flex: 1;
  padding: 0;
}

.tab {
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: background 0.3s;
  font-size: 14px;
}

.tab:hover {
  background: rgba(255, 215, 0, 0.2);
}

.tab.active {
  background: rgba(255, 215, 0, 0.3);
  border-left: 4px solid #FFD700;
}

.close-btn {
  padding: 15px;
  text-align: center;
  background: rgba(220, 38, 38, 0.8);
  color: white;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: background 0.3s;
}

.close-btn:hover {
  background: rgba(220, 38, 38, 1);
}

.main-content {
  flex: 1;
  background: linear-gradient(135deg, #4169E1, #1E90FF);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.vehicle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
  padding: 25px;
  max-width: 600px;
  width: 100%;
}

.vehicle-card {
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid #FFD700;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 3px 12px rgba(255, 215, 0, 0.3);
  color: #000080;
}

.vehicle-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
  border-color: #FFA500;
}

.vehicle-card.locked {
  background: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  opacity: 0.6;
  border-color: #999;
}

.vehicle-card.locked:hover {
  transform: none;
  box-shadow: 0 3px 12px rgba(255, 215, 0, 0.3);
}

.vehicle-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #000080;
}

.vehicle-price {
  font-size: 14px;
  color: #FFD700;
  margin-bottom: 8px;
  font-weight: 600;
}

.vehicle-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
  margin-top: 4px;
}

.status-available {
  background: #90EE90;
  color: #006400;
}

.status-locked {
  background: #FFB6C1;
  color: #8B0000;
}

.no-vehicles {
  color: white;
  font-size: 24px;
  text-align: center;
  opacity: 0.8;
}
