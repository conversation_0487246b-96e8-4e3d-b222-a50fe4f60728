local nearPed = false
local menuOpen = false

-- Vehicle list with prices and lock status
local vehicles = {
    { name = "<PERSON><PERSON>", model = "rebla", price = 25000, locked = false },
    { name = "Sunrise", model = "sunrise", price = 45000, locked = true },
    { name = "Insurgent", model = "insurgent", price = 75000, locked = true }
}

-- Create Ped at Spawn
Citizen.CreateThread(function()
    RequestModel(`s_m_m_autoshop_01`)
    while not HasModelLoaded(`s_m_m_autoshop_01`) do
        Wait(0)
    end

    local ped = CreatePed(4, `s_m_m_autoshop_01`, 219.283524, -777.178040, 30.776360, 0.0, false, true)
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
end)

-- Proximity check + open UI
Citizen.CreateThread(function()
    while true do
        Wait(0)
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        local dist = #(coords - vector3(219.283524, -777.178040, 30.776360))

        if dist < 2.0 then
            nearPed = true
            DrawText3D(219.283524, -777.178040, 31.776360, "[E] Open Vehicle Menu")
            if IsControlJustPressed(0, 38) and not menuOpen then
                SetNuiFocus(true, true)
                SendNUIMessage({ action = "show", vehicles = vehicles })
                menuOpen = true
            end
        else
            nearPed = false
        end
    end
end)

RegisterNUICallback("close", function()
    SetNuiFocus(false, false)
    SendNUIMessage({ action = "hide" })
    menuOpen = false
end)

RegisterNUICallback("spawnVehicle", function(data)
    local model = data.model
    local price = data.price

    -- TODO: Add money check here when money system is implemented
    -- if GetPlayerMoney() < price then
    --     ShowNotification("Not enough money!")
    --     return
    -- end

    RequestModel(model)
    while not HasModelLoaded(model) do Wait(100) end

    local coords = GetEntityCoords(PlayerPedId())
    local vehicle = CreateVehicle(model, coords.x, coords.y, coords.z, GetEntityHeading(PlayerPedId()), true, false)
    TaskWarpPedIntoVehicle(PlayerPedId(), vehicle, -1)
    SetVehicleNumberPlateText(vehicle, "KOTH")

    -- TODO: Deduct money when money system is implemented
    -- RemovePlayerMoney(price)

    SetNuiFocus(false, false)
    SendNUIMessage({ action = "hide" })
    menuOpen = false

    -- Optional: Show success notification
    -- ShowNotification("Vehicle spawned for $" .. price)
end)

function DrawText3D(x,y,z,text)
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(true)
    AddTextComponentString(text)
    SetDrawOrigin(x,y,z, 0)
    DrawText(0.0, 0.0)
    ClearDrawOrigin()
end
