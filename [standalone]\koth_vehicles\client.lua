local nearPed = false
local menuOpen = false

-- Vehicle list with prices and lock status
local vehicles = {
    { name = "Elegy", model = "elegy2", price = 200, locked = false },
    { name = "Zentor<PERSON>", model = "zentorno", price = 1500, locked = false },
    { name = "Adder", model = "adder", price = 2500, locked = false },
    { name = "Insurgent", model = "insurgent", price = 3000, locked = true },
    { name = "<PERSON>rum<PERSON>", model = "kuruma2", price = 3500, locked = true },
    { name = "T20", model = "t20", price = 4000, locked = true },
    { name = "Buzzard", model = "buzzard2", price = 5000, locked = true }
}

-- Create Mechanic Ped at Spawn
Citizen.CreateThread(function()
    RequestModel(`s_m_y_construct_01`)
    while not HasModelLoaded(`s_m_y_construct_01`) do
        Wait(0)
    end

    local ped = CreatePed(4, `s_m_y_construct_01`, 219.283524, -777.178040, 29.776360, 270.0, false, true)
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)

    -- Give the mechanic some tools (hammer)
    GiveWeaponToPed(ped, `WEAPON_HAMMER`, 1, false, false)
    SetCurrentPedWeapon(ped, `WEAPON_HAMMER`, true)
end)

-- Proximity check + open UI
Citizen.CreateThread(function()
    while true do
        Wait(0)
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        local dist = #(coords - vector3(219.283524, -777.178040, 29.776360))

        if dist < 2.0 then
            nearPed = true
            DrawText3D(219.283524, -777.178040, 31.776360, "[E] Open Vehicle Menu")
            if IsControlJustPressed(0, 38) and not menuOpen then
                SetNuiFocus(true, true)
                SendNUIMessage({ action = "show", vehicles = vehicles })
                menuOpen = true
            end
        else
            nearPed = false
        end
    end
end)

RegisterNUICallback("close", function()
    SetNuiFocus(false, false)
    SendNUIMessage({ action = "hide" })
    menuOpen = false
end)

RegisterNUICallback("spawnVehicle", function(data)
    local model = data.model
    local price = data.price

    -- TODO: Add money check here when money system is implemented
    -- if GetPlayerMoney() < price then
    --     ShowNotification("Not enough money!")
    --     return
    -- end

    RequestModel(model)
    while not HasModelLoaded(model) do Wait(100) end

    -- Spawn vehicle at a safe distance from the ped (5 meters away)
    local pedCoords = vector3(219.283524, -777.178040, 29.776360)
    local spawnCoords = vector3(pedCoords.x + 5.0, pedCoords.y - 3.0, pedCoords.z)
    local spawnHeading = 90.0

    -- Clear the area first
    ClearAreaOfVehicles(spawnCoords.x, spawnCoords.y, spawnCoords.z, 3.0, false, false, false, false, false)

    local vehicle = CreateVehicle(model, spawnCoords.x, spawnCoords.y, spawnCoords.z, spawnHeading, true, false)

    -- Wait for vehicle to be created
    local timeout = 0
    while not DoesEntityExist(vehicle) and timeout < 50 do
        Wait(100)
        timeout = timeout + 1
    end

    if DoesEntityExist(vehicle) then
        TaskWarpPedIntoVehicle(PlayerPedId(), vehicle, -1)
        SetVehicleNumberPlateText(vehicle, "KOTH")
        SetVehicleEngineOn(vehicle, true, true, false)

        -- Add some style
        SetVehicleModKit(vehicle, 0)
        SetVehicleWindowTint(vehicle, 1)
    end

    -- TODO: Deduct money when money system is implemented
    -- RemovePlayerMoney(price)

    SetNuiFocus(false, false)
    SendNUIMessage({ action = "hide" })
    menuOpen = false

    -- Show success notification
    ShowNotification("~g~Vehicle purchased for ~w~$" .. price .. "~g~!")
end)

function DrawText3D(x,y,z,text)
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(true)
    AddTextComponentString(text)
    SetDrawOrigin(x,y,z, 0)
    DrawText(0.0, 0.0)
    ClearDrawOrigin()
end

function ShowNotification(text)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(text)
    DrawNotification(false, false)
end
