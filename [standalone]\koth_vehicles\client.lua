local nearPed = false
local menuOpen = false

-- Vehicle list
local vehicles = {
    { name = "<PERSON>", model = "sultan" },
    { name = "<PERSON>", model = "buffalo" },
    { name = "Banshe<PERSON>", model = "banshee" },
    { name = "Dominator", model = "dominator" },
    { name = "<PERSON><PERSON><PERSON>", model = "kuru<PERSON>" }
}

-- Create Ped at Spawn
Citizen.CreateThread(function()
    RequestModel(`s_m_m_autoshop_01`)
    while not HasModelLoaded(`s_m_m_autoshop_01`) do
        Wait(0)
    end

    local ped = CreatePed(4, `s_m_m_autoshop_01`, 123.0, -1300.0, 29.0, 0.0, false, true)
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
end)

-- Proximity check + open UI
Citizen.CreateThread(function()
    while true do
        Wait(0)
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        local dist = #(coords - vector3(123.0, -1300.0, 29.0))

        if dist < 2.0 then
            nearPed = true
            DrawText3D(123.0, -1300.0, 30.0, "[E] Open Vehicle Menu")
            if IsControlJustPressed(0, 38) and not menuOpen then
                SetNuiFocus(true, true)
                SendNUIMessage({ action = "show", vehicles = vehicles })
                menuOpen = true
            end
        else
            nearPed = false
        end
    end
end)

RegisterNUICallback("close", function()
    SetNuiFocus(false, false)
    SendNUIMessage({ action = "hide" })
    menuOpen = false
end)

RegisterNUICallback("spawnVehicle", function(data)
    local model = data.model
    RequestModel(model)
    while not HasModelLoaded(model) do Wait(100) end

    local coords = GetEntityCoords(PlayerPedId())
    local vehicle = CreateVehicle(model, coords.x, coords.y, coords.z, GetEntityHeading(PlayerPedId()), true, false)
    TaskWarpPedIntoVehicle(PlayerPedId(), vehicle, -1)
    SetVehicleNumberPlateText(vehicle, "KOTH")
    SetNuiFocus(false, false)
    SendNUIMessage({ action = "hide" })
    menuOpen = false
end)

function DrawText3D(x,y,z,text)
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(true)
    AddTextComponentString(text)
    SetDrawOrigin(x,y,z, 0)
    DrawText(0.0, 0.0)
    ClearDrawOrigin()
end
