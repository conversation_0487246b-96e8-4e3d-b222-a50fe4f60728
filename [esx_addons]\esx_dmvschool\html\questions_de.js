var tableauQuestion = [
	{
		question: "Wenn du 80km/h fährst, und dich einer Wohngegend näherst musst du:",
		propositionA: "Beschleunigen",
		propositionB: "Geschwindigkeit halten, und keine anderen Fahrzeuge überholen",
		propositionC: "Verlangsamen",
		propositionD: "Geschwindigkeit beibehalten",
		reponse: "C"
	},

	{
		question: "Wenn du an einer Ampel nach rechts abbiegen möchtest, und einen Fußgänger überqueren siehst, was tust du dann?:",
		propositionA: "Den Fußgänger überfahren",
		propositionB: "Überprüfen das kein anderes Fahrzeug in der nähe ist",
		propositionC: "Du wartest bis der Fußgänger die Straße überquert hat",
		propositionD: "Du erschießt den Fußgänger und fährst weiter",
		reponse: "C"
	},

	{
		question: "Ohne einen vorherigen Hinweis, in einer Wohngegend musst du : __ km/h fahren:",
		propositionA: "30 km/h",
		propositionB: "50 km/h",
		propositionC: "40 km/h",
		propositionD: "60 km/h",
		reponse: "B"
	},

	{
		question: "Bevor einem Spurwechsel musst du:",
		propositionA: "Deine Spiegel überprüfen",
		propositionB: "Deine Blinden Stellen überprüfen",
		propositionC: "Den Spurwechsel signalisieren",
		propositionD: "Alles von oben",
		reponse: "D"
	},

	{
		question: "Welcher Blutalkoholspiegel zählt als Betrunken?",
		propositionA: "0.05%",
		propositionB: "0.18%",
		propositionC: "0.08%",
		propositionD: "0.06%",
		reponse: "C"
	},

	{
		question: "Wann kannst du an einer Ampel weiterfahren?",
		propositionA: "Wenn die Ampel Grün ist",
		propositionB: "Wenn niemand in dem bereich ist",
		propositionC: "Wenn du in einer Schulzone bist",
		propositionD: "Wenn es grün ist und niemand in dem bereich ist",
		reponse: "D"
	},

	{
		question: "Ein Fußgänger hat ein nicht überqueren Signal, was tust du?",
		propositionA: "Du lässt ihn gehen",
		propositionB: "Du beobachtest ihn bevor du weiterfährst",
		propositionC: "Du winkst ihn rüber",
		propositionD: "Du fährst weiter, weil deine Ampel grün ist.",
		reponse: "D"
	},

	{
		question: "Was ist erlaubt wenn du ein anderes Fahrzeug überholst?",
		propositionA: "Du folgst ihm nahe um ihn zu überholen",
		propositionB: "Du überholst es ohne die Straße zu verlassen",
		propositionC: "Du fährst auf der anderen Straßenseite um ihn zu überholen",
		propositionD: "Du fährst schneller als erlaubt um ihn zu überholen",
		reponse: "C"
	},

	{
		question: "Du bist auf einer Autobahn und fährst 120km/h jedoch fahren die anderen Autofahrer 125km/h also fährst du nicht schneller als:",
		propositionA: "120 km/h",
		propositionB: "125 km/h",
		propositionC: "130 km/h",
		propositionD: "110 km/h",
		reponse: "A"
	},

	{
		question: "Wenn du von einem anderen Fahrzeug überholt wirst, ist es Wichtig das du NICHT:",
		propositionA: "Verlangsamst",
		propositionB: "Deine Spiegel überprüfst",
		propositionC: "Andere Autofahrer beobachtest",
		propositionD: "Schneller wirst",
		reponse: "D"
	},
]
