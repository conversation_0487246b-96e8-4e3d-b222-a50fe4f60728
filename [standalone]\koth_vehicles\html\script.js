window.addEventListener('message', function (event) {
  if (event.data.action === "show") {
    document.body.style.display = "block";
    const container = document.getElementById("vehicle-grid");
    container.innerHTML = "";

    event.data.vehicles.forEach(v => {
      const card = document.createElement("div");
      card.className = v.locked ? "vehicle-card locked" : "vehicle-card";

      card.innerHTML = `
        <div class="vehicle-name">${v.name}</div>
        <div class="vehicle-price">$${v.price.toLocaleString()}</div>
        <div class="vehicle-status ${v.locked ? 'status-locked' : 'status-available'}">
          ${v.locked ? 'LOCKED' : 'AVAILABLE'}
        </div>
      `;

      if (!v.locked) {
        card.onclick = () => {
          fetch(`https://${GetParentResourceName()}/spawnVehicle`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({ model: v.model, price: v.price })
          });
        };
      }

      container.appendChild(card);
    });
  } else if (event.data.action === "hide") {
    document.body.style.display = "none";
  }
});

function closeMenu() {
  fetch(`https://${GetParentResourceName()}/close`, {
    method: "POST"
  });
}
