window.addEventListener('message', function (event) {
  if (event.data.action === "show") {
    document.body.style.display = "flex";
    const container = document.getElementById("vehicle-list");
    container.innerHTML = "";

    event.data.vehicles.forEach(v => {
      const btn = document.createElement("div");
      btn.className = "vehicle";
      btn.innerText = v.name;
      btn.onclick = () => {
        fetch(`https://${GetParentResourceName()}/spawnVehicle`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({ model: v.model })
        });
      };
      container.appendChild(btn);
    });
  } else if (event.data.action === "hide") {
    document.body.style.display = "none";
  }
});

function closeMenu() {
  fetch(`https://${GetParentResourceName()}/close`, {
    method: "POST"
  });
}
