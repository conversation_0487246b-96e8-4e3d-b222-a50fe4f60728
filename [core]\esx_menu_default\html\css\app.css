@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@500;600&display=swap");

::-webkit-scrollbar {
    display: none;
}

.menu {
    font-family: "Poppins", sans-serif;
    min-width: 350px;
    color: #fff;
    position: absolute;
    background: rgba(33, 33, 33, 0);
    text-align: center;
}

.head {
    display: block;
    overflow: hidden;
    padding-bottom: 3px;
    text-align: center;
    margin-bottom: 5px;
    white-space: nowrap;
    background: rgba(10, 10, 10, 0.9);
}

.menu .head {
    text-align: center;
    height: 28px;
    color: #ffffff;
    font-weight: 700;
    font-size: 20px;
}

.menu .menu-items {
    max-height: 450px;
    overflow-y: auto;
    font-weight: 550;
}

.menu-items {
    margin-bottom: 2px;
}

.menu .menu-items .menu-item {
    display: block;
    padding: 7px;
    font-size: 13px;
    height: 16px;
    text-indent: 5px;
    margin-top: 5px;
    background: rgba(40, 40, 40, 0.8);
    color: rgb(202, 202, 202);
    border-radius: 5px;
    margin-bottom: 6px;
}

.menu .menu-items .menu-item.selected {
    border: 1px solid rgba(55, 55, 55, 0.9);
    background: rgba(20, 20, 20, 0.9);
    color: rgba(243, 243, 243);
    letter-spacing: 0.2px;
    font-weight: 500;
}

.menu.align-left {
    left: 0;
    top: 50%;
}

.menu.align-top-left {
    left: 3rem;
    top: 0;
}

.menu.align-top {
    left: 50%;
    top: 0;
}

.menu.align-top-right {
    right: 3rem;
    top: 0;
}

.menu.align-right {
    right: 0;
    top: 50%;
}

.menu.align-bottom-right {
    right: 3rem;
    bottom: 0;
}

.menu.align-bottom {
    left: 50%;
    bottom: 0;
    transform: translate(0, -50%);
}

.menu.align-bottom-left {
    left: 3rem;
    bottom: 0;
}

.menu.align-center {
    left: 50%;
    top: 35%;
    transform: translate(-50%, 50%);
}
