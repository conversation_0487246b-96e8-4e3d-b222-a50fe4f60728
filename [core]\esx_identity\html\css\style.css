@import url("https://fonts.googleapis.com/css2?family=Oswald&display=swap");

body {
    font-family: sans-serif;
    overflow: hidden;
    background-color: transparent;
}

.none {
    display: none;
}

.dialog {
    width: 332px;
    opacity: 0.95;
    padding: 20px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #152029;
    border-radius: 10px;
    box-shadow: 0 -5px 3px -3px #21303d, 0 5px 3px -3px #21303d;
    border: none;
    color: #ffffff;
}

.title {
    font-family: "<PERSON>", sans-serif;
    font-size: 22px;
    text-align: center;
    padding: 5px;
    margin-bottom: 20px;
}

input {
    margin-bottom: 15px;
    border: none;
    border-bottom: 2px solid #58636c;
    width: 100%;
    outline: none;
    padding: 10px;
    padding-left: 0;
    font-family: "<PERSON>", sans-serif;
    color: #ffffff;
    text-align: left;
    background-color: #152029;
}

::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: rgba(84, 97, 105, 255);
    font-family: "<PERSON>", sans-serif;
    font-weight: 200;
    opacity: 1;
    /* Firefox */
}

.radio-toolbar input[type="radio"] {
    opacity: 0;
    position: absolute;
    width: 36%;
}

.radio-toolbar label {
    display: inline-block;
    margin-top: 5px;
    background-color: rgba(15, 15, 15, 0.9);
    padding: 10px 20px;
    font-family: "Oswald", sans-serif;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    border: none;
    border-radius: 5px;
    width: 36%;
}

.radio-toolbar input[type="radio"]:checked + label {
    width: 36%;
    background-color: rgba(15, 15, 15, 0.9);
    border: none;
    border-bottom: 1px solid #93a3b6;
    border-radius: 5px;
    color: #ffffff;
}

.radio-toolbar input[type="radio"]:focus + label {
    background-color: rgba(15, 15, 15, 0.9);
    border: none;
    border-bottom: 1px solid #93a3b6;
    border-radius: 5px;
    color: #ffffff;
}

.radio-toolbar label:hover {
    background-color: rgba(28, 24, 24, 0.931);
    width: 36%;
    color: #ffffff;
}

button {
    display: block;
    margin-top: 35px;
    /*padding: 10px;*/
    background-color: #4569c6;
    outline: none;
    border: 2px double rgba(40, 40, 40, 0.9);
    color: #ffffff;
    height: 30px;
    width: 100%;
}

h1 {
    display: block;
    margin-top: 5px;
    margin-right: 5px;
    padding: 10px;
    background-color: rgba(15, 15, 15, 0.9);
    color: #ffffff;
    width: 93%;
    text-align: center;
}

.range-wrap {
    position: relative;
    margin: 0 auto 3rem;
}

.range {
    width: 100%;
}

.bubble {
    background: rgba(15, 15, 15, 0.9);
    color: #ffffff;
    padding: 4px 12px;
    position: absolute;
    border-radius: 4px;
    left: 50%;
    transform: translateX(-50%);
}

.bubble::after {
    content: "";
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(15, 15, 15, 0.9);
    color: black;
    top: -1px;
    left: 50%;
}
