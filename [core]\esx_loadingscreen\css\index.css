/**
 * Copyright (c) <PERSON><PERSON><PERSON><PERSON>
 *
 * All rights reserved.
 *
 * Even if 'All rights reserved' is very clear :
 *
 *   You shall not use any piece of this software in a commercial product / service
 *   You shall not resell this software
 *   You shall not provide any facility to install this particular software in a commercial product / service
 *   If you redistribute this software, you must link to ORIGINAL repository at https://github.com/esx-framework/esx-reborn
 *   This copyright should appear in every part of the project code
 */

html,
body {
    margin: 0;
    padding: 0;
    font-family: "Quicksand", sans-serif;
    overflow: hidden;
}

#esx_intro {
    z-index: 2;
}

#esx_loop {
    z-index: 1;
}

#loading_txt {
    bottom: 5%;
    left: 50%;
    margin-left: -80px;
    position: absolute;
    z-index: 999;

    -webkit-animation: fadein 5s;
    -moz-animation: fadein 5s;
    -ms-animation: fadein 5s;
    -o-animation: fadein 5s;
    animation: fadein 5s;
}

p {
    color: white;
    font-size: 32px;
}

.text_thing:after {
    content: " .";
    animation: dots 2s steps(5, end) infinite;
}

@keyframes dots {
    0%,
    20% {
        color: rgba(0, 0, 0, 0);
        text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0), 0.5em 0 0 rgba(0, 0, 0, 0);
    }
    40% {
        color: white;
        text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0), 0.5em 0 0 rgba(0, 0, 0, 0);
    }
    60% {
        text-shadow: 0.25em 0 0 white, 0.5em 0 0 rgba(0, 0, 0, 0);
    }
    80%,
    100% {
        text-shadow: 0.25em 0 0 white, 0.5em 0 0 white;
    }
}

@keyframes fadein {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@-moz-keyframes fadein {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@-webkit-keyframes fadein {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@-ms-keyframes fadein {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@-o-keyframes fadein {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
