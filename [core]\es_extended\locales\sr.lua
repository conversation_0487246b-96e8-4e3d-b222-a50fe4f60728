Locales["sr"] = {
    -- Inventory
    ["inventory"] = "Inventar ( Težina %s / %s )",
    ["use"] = "Koristi",
    ["give"] = "Daj",
    ["remove"] = "Baci",
    ["return"] = "Nazad",
    ["give_to"] = "Daj",
    ["amount"] = "Količina",
    ["giveammo"] = "Daj municiju",
    ["amountammo"] = "Količina municije",
    ["noammo"] = "Nemate dovoljno!",
    ["gave_item"] = "Davanje %sx %s igraču %s",
    ["received_item"] = "Dobijeno %sx %s od %s",
    ["gave_weapon"] = "Davanje %s igraču %s",
    ["gave_weapon_ammo"] = "Davanje ~o~%sx %s za %s igraču %s",
    ["gave_weapon_withammo"] = "Davanje %s sa ~o~%sx %s igraču %s",
    ["gave_weapon_hasalready"] = "%s već ima %s",
    ["gave_weapon_noweapon"] = "%s nema to oružje",
    ["received_weapon"] = "Dobijeno %s od %s",
    ["received_weapon_ammo"] = "Dobijeno ~o~%sx %s za vaš %s od %s",
    ["received_weapon_withammo"] = "Dobijeno %s sa ~o~%sx %s od %s",
    ["received_weapon_hasalready"] = "%s je pokušao da Vam da %s, ali vi već imate to oružje",
    ["received_weapon_noweapon"] = "%s je pokušao da Vam da municiju za %s, ali vi nemate to oružje",
    ["gave_account_money"] = "Davanje $%s (%s) igraču %s",
    ["received_account_money"] = "Dobijeno $%s (%s) od %s",
    ["amount_invalid"] = "Nevažeća količina",
    ["players_nearby"] = "Nema igrača u blizini",
    ["ex_inv_lim"] = "Ne možete uraditi to, premašuje max težinu od %s",
    ["imp_invalid_quantity"] = "Nevažeća količina",
    ["imp_invalid_amount"] = "Nevažeći iznos",
    ["threw_standard"] = "Bacanje %sx %s",
    ["threw_account"] = "Bacanje $%s %s",
    ["threw_weapon"] = "Bacanje %s",
    ["threw_weapon_ammo"] = "Bacanje %s sa ~o~%sx %s",
    ["threw_weapon_already"] = "Vi već imate to oružje",
    ["threw_cannot_pickup"] = "Inventar je pun, ne možete pokupiti to!",
    ["threw_pickup_prompt"] = "Pritisni E da pokupiš",

    -- Key mapping
    ["keymap_showinventory"] = "Otvaranje inventara",

    -- Salary related
    ["received_salary"] = "Plaćeno Vam je: $%s",
    ["received_help"] = "Isplaćen Vam je ček: $%s",
    ["company_nomoney"] = "Kompanija u kojoj ste zapošljeni nema više novca",
    ["received_paycheck"] = "primili ste platu",
    ["bank"] = "Maze Banka",
    ["account_bank"] = "Banka",
    ["account_black_money"] = "Prljav novac",
    ["account_money"] = "Novac",

    ["act_imp"] = "Ne možete izvršiti radnju",
    ["in_vehicle"] = "Ne možete uraditi to dok je igrač u vozilu",
    ["not_in_vehicle"] = "Cannot Perform Action, Player isn't in a vehicle",

    -- Commands
    ["command_bring"] = "TP-ajte igrača do Vas",
    ["command_car"] = "Stvorite vozilo",
    ["command_car_car"] = "Model ili hash vozila",
    ["command_cardel"] = "Obrišite vozilo u blizini",
    ["command_cardel_radius"] = "Obrišite sva vozila unutar navedenog radiusa",
    ["command_repair"] = "Repair your vehicle",
    ["command_repair_success"] = "Successfully repaired vehicle",
    ["command_repair_success_target"] = "An admin repaired your vehicle",
    ["command_clear"] = "Obrišite chat",
    ["command_clearall"] = "Obrišite chat za sve igrače",
    ["command_clearinventory"] = "Obrišite sve stvari iz inventara igrača",
    ["command_clearloadout"] = "Obrišite sva oružja iz inventara igrača",
    ["command_freeze"] = "Zaledite igrača",
    ["command_unfreeze"] = "Odledite igrača",
    ["command_giveaccountmoney"] = "Dajte novac na odredjeni nalog",
    ["command_giveaccountmoney_account"] = "Nalog za slanje",
    ["command_giveaccountmoney_amount"] = "Količina",
    ["command_giveaccountmoney_invalid"] = "Nalog nepostojeći",
    ["command_giveitem"] = "Dajte item igraču",
    ["command_giveitem_item"] = "Ime item-a",
    ["command_giveitem_count"] = "Količina",
    ["command_giveweapon"] = "Dajte oružje igraču",
    ["command_giveweapon_weapon"] = "Ime oružja",
    ["command_giveweapon_ammo"] = "Količina municije",
    ["command_giveweapon_hasalready"] = "Igrač već ima to oružje",
    ["command_giveweaponcomponent"] = "Dajte dodatak za oružje igraču",
    ["command_giveweaponcomponent_component"] = "Ime dodatka",
    ["command_giveweaponcomponent_invalid"] = "Nevažeći dodatak",
    ["command_giveweaponcomponent_hasalready"] = "Igrač već ima taj dodatak",
    ["command_giveweaponcomponent_missingweapon"] = "Igrač nema oružje",
    ["command_goto"] = "Idite do igrača",
    ["command_kill"] = "Ubijte igrača",
    ["command_save"] = "Forsirajte čuvanje date igrača",
    ["command_saveall"] = "Forsirajte čuvanje date svih igrača",
    ["command_setaccountmoney"] = "Postavite novac na određeni račun",
    ["command_setaccountmoney_amount"] = "Količina",
    ["command_setcoords"] = "Teleportujte se na koordinate",
    ["command_setcoords_x"] = "X",
    ["command_setcoords_y"] = "Y",
    ["command_setcoords_z"] = "Z",
    ["command_setjob"] = "Postavite posao igraču",
    ["command_setjob_job"] = "Ime",
    ["command_setjob_grade"] = "Stepen posla",
    ["command_setjob_invalid"] = "posao, stepen ili oba nisu validna",
    ["command_setgroup"] = "Postavite permisiju",
    ["command_setgroup_group"] = "Ime grupe",
    ["commanderror_argumentmismatch"] = "Nevazeci broj argumenata (položio %s, željeno %s)",
    ["commanderror_argumentmismatch_number"] = "Nevažeći argument #%s tip podataka (položeno, željeno)",
    ["commanderror_argumentmismatch_string"] = "Invalid Argument #%s data type (passed number, wanted string)",
    ["commanderror_invaliditem"] = "Nevažeći item",
    ["commanderror_invalidweapon"] = "Nevažeće oružje",
    ["commanderror_console"] = "Komanda se ne može izvršiti iz konzole",
    ["commanderror_invalidcommand"] = "Nevažeća komanda - /%s",
    ["commanderror_invalidplayerid"] = "Igrač nije online",
    ["commandgeneric_playerid"] = "Igračev server ID",
    ["command_giveammo_noweapon_found"] = "%s nema to oružje",
    ["command_giveammo_weapon"] = "Ime oružja",
    ["command_giveammo_ammo"] = "Količina municije",
    ["tpm_nowaypoint"] = "Morate označiti lokaciju.",
    ["tpm_success"] = "Teleportovani ste na lokaciju",

    ["noclip_message"] = "Noclip %s",
    ["enabled"] = "~g~upaljen~s~",
    ["disabled"] = "~r~ugašen~s~",

    -- Locale settings
    ["locale_digit_grouping_symbol"] = ",",
    ["locale_currency"] = "£%s",

    -- Weapons

    -- Melee
    ["weapon_dagger"] = "Dagger",
    ["weapon_bat"] = "Bat",
    ["weapon_battleaxe"] = "Battle Axe",
    ["weapon_bottle"] = "Bottle",
    ["weapon_crowbar"] = "Crowbar",
    ["weapon_flashlight"] = "Flashlight",
    ["weapon_golfclub"] = "Golf Club",
    ["weapon_hammer"] = "Hammer",
    ["weapon_hatchet"] = "Hatchet",
    ["weapon_knife"] = "Knife",
    ["weapon_knuckle"] = "Knuckledusters",
    ["weapon_machete"] = "Machete",
    ["weapon_nightstick"] = "Nightstick",
    ["weapon_wrench"] = "Pipe Wrench",
    ["weapon_poolcue"] = "Pool Cue",
    ["weapon_stone_hatchet"] = "Stone Hatchet",
    ["weapon_switchblade"] = "Switchblade",

    -- Handguns
    ["weapon_appistol"] = "AP Pistol",
    ["weapon_ceramicpistol"] = "Ceramic Pistol",
    ["weapon_combatpistol"] = "Combat Pistol",
    ["weapon_doubleaction"] = "Double-Action Revolver",
    ["weapon_navyrevolver"] = "Navy Revolver",
    ["weapon_flaregun"] = "Flaregun",
    ["weapon_gadgetpistol"] = "Gadget Pistol",
    ["weapon_heavypistol"] = "Heavy Pistol",
    ["weapon_revolver"] = "Heavy Revolver",
    ["weapon_revolver_mk2"] = "Heavy Revolver MK2",
    ["weapon_marksmanpistol"] = "Marksman Pistol",
    ["weapon_pistol"] = "Pistol",
    ["weapon_pistol_mk2"] = "Pistol MK2",
    ["weapon_pistol50"] = "Pistol .50",
    ["weapon_snspistol"] = "SNS Pistol",
    ["weapon_snspistol_mk2"] = "SNS Pistol MK2",
    ["weapon_stungun"] = "Taser",
    ["weapon_raypistol"] = "Up-N-Atomizer",
    ["weapon_vintagepistol"] = "Vintage Pistol",

    -- Shotguns
    ["weapon_assaultshotgun"] = "Assault Shotgun",
    ["weapon_autoshotgun"] = "Auto Shotgun",
    ["weapon_bullpupshotgun"] = "Bullpup Shotgun",
    ["weapon_combatshotgun"] = "Combat Shotgun",
    ["weapon_dbshotgun"] = "Double Barrel Shotgun",
    ["weapon_heavyshotgun"] = "Heavy Shotgun",
    ["weapon_musket"] = "Musket",
    ["weapon_pumpshotgun"] = "Pump Shotgun",
    ["weapon_pumpshotgun_mk2"] = "Pump Shotgun MK2",
    ["weapon_sawnoffshotgun"] = "Sawed Off Shotgun",

    -- SMG & LMG
    ["weapon_assaultsmg"] = "Assault SMG",
    ["weapon_combatmg"] = "Combat MG",
    ["weapon_combatmg_mk2"] = "Combat MG MK2",
    ["weapon_combatpdw"] = "Combat PDW",
    ["weapon_gusenberg"] = "Gusenberg Sweeper",
    ["weapon_machinepistol"] = "Machine Pistol",
    ["weapon_mg"] = "MG",
    ["weapon_microsmg"] = "Micro SMG",
    ["weapon_minismg"] = "Mini SMG",
    ["weapon_smg"] = "SMG",
    ["weapon_smg_mk2"] = "SMG MK2",
    ["weapon_raycarbine"] = "Unholy Hellbringer",

    -- Rifles
    ["weapon_advancedrifle"] = "Advanced Rifle",
    ["weapon_assaultrifle"] = "Assault Rifle",
    ["weapon_assaultrifle_mk2"] = "Assault Rifle MK2",
    ["weapon_bullpuprifle"] = "Bullpup Rifle",
    ["weapon_bullpuprifle_mk2"] = "Bullpup Rifle MK2",
    ["weapon_carbinerifle"] = "Carbine Rifle",
    ["weapon_carbinerifle_mk2"] = "Carbine Rifle MK2",
    ["weapon_compactrifle"] = "Compact Rifle",
    ["weapon_militaryrifle"] = "Military Rifle",
    ["weapon_specialcarbine"] = "Special Carbine",
    ["weapon_specialcarbine_mk2"] = "Special Carbine MK2",
    ["weapon_heavyrifle"] = "Heavy Rifle", -- Not Translated

    -- Sniper
    ["weapon_heavysniper"] = "Heavy Sniper",
    ["weapon_heavysniper_mk2"] = "Heavy Sniper MK2",
    ["weapon_marksmanrifle"] = "Marksman Rifle",
    ["weapon_marksmanrifle_mk2"] = "Marksman Rifle MK2",
    ["weapon_sniperrifle"] = "Sniper Rifle",

    -- Heavy / Launchers
    ["weapon_compactlauncher"] = "Compact Launcher",
    ["weapon_firework"] = "Firework Launcher",
    ["weapon_grenadelauncher"] = "Grenade Launcher",
    ["weapon_hominglauncher"] = "Homing Launcher",
    ["weapon_minigun"] = "Minigun",
    ["weapon_railgun"] = "Railgun",
    ["weapon_rpg"] = "Rocket Launcher",
    ["weapon_rayminigun"] = "Widowmaker",

    -- Criminal Enterprises DLC
    ["weapon_metaldetector"] = "Metal Detector",
    ["weapon_precisionrifle"] = "Precision Rifle",
    ["weapon_tactilerifle"] = "Service Carbine",

    -- Drug Wars DLC
    ["weapon_candycane"] = "Candy Cane", -- not translated
    ["weapon_acidpackage"] = "Acid Package", -- not translated
    ["weapon_pistolxm3"] = "WM 29 Pistol", -- not translated
    ["weapon_railgunxm3"] = "Railgun", -- not translated

    -- Thrown
    ["weapon_ball"] = "Baseball",
    ["weapon_bzgas"] = "BZ Gas",
    ["weapon_flare"] = "Flare",
    ["weapon_grenade"] = "Grenade",
    ["weapon_petrolcan"] = "Jerrycan",
    ["weapon_hazardcan"] = "Hazardous Jerrycan",
    ["weapon_molotov"] = "Molotov Cocktail",
    ["weapon_proxmine"] = "Proximity Mine",
    ["weapon_pipebomb"] = "Pipe Bomb",
    ["weapon_snowball"] = "Snowball",
    ["weapon_stickybomb"] = "Sticky Bomb",
    ["weapon_smokegrenade"] = "Tear Gas",

    -- Special
    ["weapon_fireextinguisher"] = "Fire Extinguisher",
    ["weapon_digiscanner"] = "Digital Scanner",
    ["weapon_garbagebag"] = "Garbage Bag",
    ["weapon_handcuffs"] = "Handcuffs",
    ["gadget_nightvision"] = "Night Vision",
    ["gadget_parachute"] = "parachute",

    -- Weapon Components
    ["component_knuckle_base"] = "base Model",
    ["component_knuckle_pimp"] = "the Pimp",
    ["component_knuckle_ballas"] = "the Ballas",
    ["component_knuckle_dollar"] = "the Hustler",
    ["component_knuckle_diamond"] = "the Rock",
    ["component_knuckle_hate"] = "the Hater",
    ["component_knuckle_love"] = "the Lover",
    ["component_knuckle_player"] = "the Player",
    ["component_knuckle_king"] = "the King",
    ["component_knuckle_vagos"] = "the Vagos",

    ["component_luxary_finish"] = "luxary Weapon Finish",

    ["component_handle_default"] = "default Handle",
    ["component_handle_vip"] = "vIP Handle",
    ["component_handle_bodyguard"] = "bodyguard Handle",

    ["component_vip_finish"] = "vIP Finish",
    ["component_bodyguard_finish"] = "bodyguard Finish",

    ["component_camo_finish"] = "digital Camo",
    ["component_camo_finish2"] = "brushstroke Camo",
    ["component_camo_finish3"] = "woodland Camo",
    ["component_camo_finish4"] = "skull Camo",
    ["component_camo_finish5"] = "sessanta Nove Camo",
    ["component_camo_finish6"] = "perseus Camo",
    ["component_camo_finish7"] = "leopard Camo",
    ["component_camo_finish8"] = "zebra Camo",
    ["component_camo_finish9"] = "geometric Camo",
    ["component_camo_finish10"] = "boom Camo",
    ["component_camo_finish11"] = "patriotic Camo",

    ["component_camo_slide_finish"] = "digital Slide Camo",
    ["component_camo_slide_finish2"] = "brushstroke Slide Camo",
    ["component_camo_slide_finish3"] = "woodland Slide Camo",
    ["component_camo_slide_finish4"] = "skull Slide Camo",
    ["component_camo_slide_finish5"] = "sessanta Nove Slide Camo",
    ["component_camo_slide_finish6"] = "perseus Slide Camo",
    ["component_camo_slide_finish7"] = "leopard Slide Camo",
    ["component_camo_slide_finish8"] = "zebra Slide Camo",
    ["component_camo_slide_finish9"] = "geometric Slide Camo",
    ["component_camo_slide_finish10"] = "boom Slide Camo",
    ["component_camo_slide_finish11"] = "patriotic Slide Camo",

    ["component_clip_default"] = "default Magazine",
    ["component_clip_extended"] = "extended Magazine",
    ["component_clip_drum"] = "drum Magazine",
    ["component_clip_box"] = "box Magazine",

    ["component_scope_holo"] = "holographic Scope",
    ["component_scope_small"] = "small Scope",
    ["component_scope_medium"] = "medium Scope",
    ["component_scope_large"] = "large Scope",
    ["component_scope"] = "mounted Scope",
    ["component_scope_advanced"] = "advanced Scope",
    ["component_ironsights"] = "ironsights",

    ["component_suppressor"] = "suppressor",
    ["component_compensator"] = "compensator",

    ["component_muzzle_flat"] = "flat Muzzle Brake",
    ["component_muzzle_tactical"] = "tactical Muzzle Brake",
    ["component_muzzle_fat"] = "fat-End Muzzle Brake",
    ["component_muzzle_precision"] = "precision Muzzle Brake",
    ["component_muzzle_heavy"] = "heavy Duty Muzzle Brake",
    ["component_muzzle_slanted"] = "slanted Muzzle Brake",
    ["component_muzzle_split"] = "split-End Muzzle Brake",
    ["component_muzzle_squared"] = "squared Muzzle Brake",

    ["component_flashlight"] = "flashlight",
    ["component_grip"] = "grip",

    ["component_barrel_default"] = "default Barrel",
    ["component_barrel_heavy"] = "heavy Barrel",

    ["component_ammo_tracer"] = "tracer Ammo",
    ["component_ammo_incendiary"] = "incendiary Ammo",
    ["component_ammo_hollowpoint"] = "hollowpoint Ammo",
    ["component_ammo_fmj"] = "fMJ Ammo",
    ["component_ammo_armor"] = "armor Piercing Ammo",
    ["component_ammo_explosive"] = "armor Piercing Incendiary Ammo",

    ["component_shells_default"] = "default Shells",
    ["component_shells_incendiary"] = "dragons Breath Shells",
    ["component_shells_armor"] = "steel Buckshot Shells",
    ["component_shells_hollowpoint"] = "flechette Shells",
    ["component_shells_explosive"] = "explosive Slug Shells",

    -- Weapon Ammo
    ["ammo_rounds"] = "round(s)",
    ["ammo_shells"] = "shell(s)",
    ["ammo_charge"] = "charge",
    ["ammo_petrol"] = "gallons of fuel",
    ["ammo_firework"] = "firework(s)",
    ["ammo_rockets"] = "rocket(s)",
    ["ammo_grenadelauncher"] = "grenade(s)",
    ["ammo_grenade"] = "grenade(s)",
    ["ammo_stickybomb"] = "bomb(s)",
    ["ammo_pipebomb"] = "bomb(s)",
    ["ammo_smokebomb"] = "bomb(s)",
    ["ammo_molotov"] = "cocktail(s)",
    ["ammo_proxmine"] = "mine(s)",
    ["ammo_bzgas"] = "can(s)",
    ["ammo_ball"] = "ball(s)",
    ["ammo_snowball"] = "snowball(s)",
    ["ammo_flare"] = "flare(s)",
    ["ammo_flaregun"] = "flare(s)",

    -- Weapon Tints
    ["tint_default"] = "default skin",
    ["tint_green"] = "green skin",
    ["tint_gold"] = "gold skin",
    ["tint_pink"] = "pink skin",
    ["tint_army"] = "army skin",
    ["tint_lspd"] = "blue skin",
    ["tint_orange"] = "orange skin",
    ["tint_platinum"] = "platinum skin",
}
