<h1 align='center'>[ESX] TextUI</a></h1><p align='center'><b><a href='https://discord.esx-framework.org/'>Discord</a> - <a href='https://esx-framework.org/'>Website</a> - <a href='https://documentation.esx-framework.org/legacy/installation'>Documentation</a></b></h5>

A beautiful and simple Persistent Notification.

# Example Code

<h3>Change style type</h3>

```lua
---usage: message/type
ESX.TextUI("i ~r~love~s~ donuts", "error")
ESX.TextUI("i ~g~love~s~ donuts", "success")
ESX.TextUI("i ~b~love~s~ donuts", "info")
```

<h3>Hide the TextUI</h3>

```lua
ESX.HideUI()
```

<h3>Color Code Usage</h3>

```lua
~r~ = Red
~b~ = Blue
~g~ = Green
~y~ = Yellow
~p~ = Purple
~c~ = Grey
~m~ = Dark Grey
~u~ = Black
~o~ = Orange

ESX.TextUI("i ~r~love~s~ donuts", "error")
```

# Previews

![Preview_1](https://cdn.discordapp.com/attachments/944789399852417096/997894930678693959/unknown.png)

## Legal

esx_textui - Persistant Notifications!

Copyright (C) 2023 ESX-Framework

This program Is free software: you can redistribute it And/Or modify it under the terms Of the GNU General Public License As published by the Free Software Foundation, either version 3 Of the License, Or (at your option) any later version.

This program Is distributed In the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty Of MERCHANTABILITY Or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License For more details.

You should have received a copy Of the GNU General Public License along with this program. If Not, see <http://www.gnu.org/licenses/>.
