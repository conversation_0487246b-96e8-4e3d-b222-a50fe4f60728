-- Server-side script for KOTH Vehicle Menu
-- Currently minimal, can be expanded for logging, permissions, etc.

print("^2[KOTH Vehicles]^7 Resource started successfully!")

-- Example: Log vehicle spawns (optional)
RegisterServerEvent('koth_vehicles:logSpawn')
AddEventHandler('koth_vehicles:logSpawn', function(vehicleModel)
    local source = source
    local playerName = GetPlayerName(source)
    print(string.format("^3[KOTH Vehicles]^7 %s spawned a %s", playerName, vehicleModel))
end)
